import openai
import time
from config import Config
from utils.logger import log_api_call, log_error

class OpenRouterService:
    """Servicio para interactuar con la API de OpenRouter"""
    
    def __init__(self):
        self.client = openai.OpenAI(
            base_url=Config.OPENROUTER_BASE_URL,
            api_key=Config.OPENROUTER_API_KEY
        )
        self.model = Config.OPENROUTER_MODEL
        
        # Prompt del sistema para el chatbot
        self.system_prompt = """Eres un asistente virtual especializado en prevención de violencia familiar y de género. Tu objetivo es:

1. ESCUCHAR con empatía y sin juzgar
2. VALIDAR las emociones y experiencias de la persona
3. IDENTIFICAR señales de violencia de manera sutil
4. OFRECER apoyo emocional y orientación
5. PROPORCIONAR información sobre recursos de ayuda cuando sea apropiado

DIRECTRICES IMPORTANTES:
- Usa un lenguaje cálido, empático y comprensivo
- No minimices ni juzgues las situaciones
- Haz preguntas abiertas para entender mejor
- Respeta el ritmo de la persona
- Mantén la confidencialidad
- Si detectas riesgo inmediato, sugiere buscar ayuda profesional
- Usa un español natural y cercano
- Evita tecnicismos o lenguaje clínico
- Respuestas de máximo 150 palabras

NUNCA:
- Des consejos legales específicos
- Diagnostiques situaciones
- Presiones para tomar decisiones
- Reveles información personal
- Uses lenguaje alarmista

Recuerda: Tu rol es acompañar, escuchar y orientar hacia recursos profesionales."""

    def generate_response(self, user_message, conversation_history=None, risk_analysis=None, session_id=None):
        """Generar respuesta usando OpenRouter"""
        
        start_time = time.time()
        
        try:
            # Construir el contexto de la conversación
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Agregar historial de conversación si existe
            if conversation_history:
                for msg in conversation_history[-6:]:  # Últimos 6 mensajes para contexto
                    messages.append({
                        "role": msg.get("role", "user"),
                        "content": msg.get("content", "")
                    })
            
            # Agregar mensaje actual del usuario
            messages.append({"role": "user", "content": user_message})
            
            # Modificar el prompt si hay análisis de riesgo
            if risk_analysis:
                context_addition = self._build_risk_context(risk_analysis)
                if context_addition:
                    messages[0]["content"] += f"\n\nCONTEXTO ACTUAL: {context_addition}"
            
            # Llamada a la API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=300,
                temperature=0.7,
                top_p=0.9
            )
            
            response_time = time.time() - start_time
            
            # Log de la llamada exitosa
            log_api_call(session_id, "OpenRouter", "SUCCESS", response_time)
            
            return {
                'success': True,
                'response': response.choices[0].message.content.strip(),
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            error_message = str(e)
            
            # Log del error
            log_api_call(session_id, "OpenRouter", "ERROR", response_time)
            log_error(session_id, error_message, "OPENROUTER_API")
            
            return {
                'success': False,
                'error': error_message,
                'response': self._get_fallback_response(risk_analysis)
            }
    
    def _build_risk_context(self, risk_analysis):
        """Construir contexto adicional basado en el análisis de riesgo"""
        if not risk_analysis:
            return ""
        
        risk_level = risk_analysis.get('risk_level', 'bajo')
        violence_type = risk_analysis.get('violence_type')
        
        context_parts = []
        
        if risk_level == 'alto':
            context_parts.append("La persona puede estar en una situación de alto riesgo.")
            context_parts.append("Prioriza su seguridad y sugiere recursos de ayuda inmediata.")
        elif risk_level == 'medio':
            context_parts.append("Se detectan señales de posible violencia.")
            context_parts.append("Ofrece apoyo y explora la situación con cuidado.")
        
        if violence_type:
            type_contexts = {
                'física': "Posible violencia física detectada.",
                'psicológica': "Posible violencia psicológica/emocional detectada.",
                'sexual': "Posible violencia sexual detectada.",
                'económica': "Posible violencia económica detectada.",
                'digital': "Posible violencia digital detectada."
            }
            if violence_type in type_contexts:
                context_parts.append(type_contexts[violence_type])
        
        return " ".join(context_parts)
    
    def _get_fallback_response(self, risk_analysis=None):
        """Respuesta de respaldo cuando falla la API"""
        
        if risk_analysis and risk_analysis.get('risk_level') == 'alto':
            return """Entiendo que estás pasando por una situación muy difícil. Tu seguridad es lo más importante.

Te recomiendo contactar de inmediato:
📞 Emergencias: 911
📞 Línea Violeta: 646-182-30-00 ext. 2743

No estás sola. Hay personas capacitadas que pueden ayudarte."""
        
        elif risk_analysis and risk_analysis.get('risk_level') == 'medio':
            return """Gracias por confiar en mí y compartir lo que estás viviendo. Lo que describes son señales importantes que no debes ignorar.

¿Te gustaría que conversemos más sobre esto? También puedo proporcionarte información sobre recursos de apoyo disponibles."""
        
        else:
            return """Te escucho y entiendo que esto puede ser difícil de compartir. Estoy aquí para apoyarte.

¿Puedes contarme un poco más sobre cómo te sientes en esta situación?"""
    
    def generate_emergency_response(self, session_id=None):
        """Generar respuesta específica para situaciones de emergencia"""
        
        emergency_response = f"""🚨 **TU SEGURIDAD ES PRIORITARIA** 🚨

Si estás en peligro inmediato, contacta:

📞 **EMERGENCIAS: 911**
📞 **Línea Violeta: 646-182-30-00 ext. 2743**
📞 **Denuncia Anónima: 089**

🏢 **Atención presencial:**
Subcentro de Seguridad Ensenada
📍 {Config.EMERGENCY_CONTACTS['subcentro_seguridad']['direccion']}
📞 {Config.EMERGENCY_CONTACTS['subcentro_seguridad']['telefono']}

**Recuerda:**
• No estás sola
• La violencia NO es tu culpa
• Hay ayuda disponible las 24 horas
• Tu vida tiene valor

Si no puedes hablar, envía un mensaje de texto o busca ayuda de personas de confianza."""

        return emergency_response
    
    def test_connection(self):
        """Probar la conexión con OpenRouter"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hola, ¿puedes responder con 'Conexión exitosa'?"}],
                max_tokens=10
            )
            return True, "Conexión exitosa con OpenRouter"
        except Exception as e:
            return False, f"Error de conexión: {str(e)}"
