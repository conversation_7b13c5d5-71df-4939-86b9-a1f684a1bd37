import hashlib
from datetime import datetime
from models.risk_analyzer import RiskAnalyzer
from services.openrouter_service import OpenRouterService
from utils.database import get_or_create_conversation, save_message, db
from utils.logger import (
    log_conversation_start, log_message_processed, 
    log_emergency_contact_provided, log_high_risk_detected,
    hash_message
)
from config import Config

class ChatbotService:
    """Servicio principal del chatbot que coordina todos los componentes"""
    
    def __init__(self):
        self.risk_analyzer = RiskAnalyzer()
        self.openrouter_service = OpenRouterService()
        self.conversation_cache = {}  # Cache temporal para conversaciones activas
    
    def start_conversation(self, session_id):
        """Iniciar una nueva conversación"""
        log_conversation_start(session_id)
        
        # Crear o recuperar conversación en BD
        conversation = get_or_create_conversation(session_id)
        
        # Inicializar cache de conversación
        self.conversation_cache[session_id] = {
            'conversation_id': conversation.id,
            'messages': [],
            'risk_history': [],
            'start_time': datetime.utcnow()
        }
        
        return {
            'message': Config.WELCOME_MESSAGE,
            'conversation_id': conversation.id,
            'session_id': session_id
        }
    
    def process_message(self, session_id, user_message):
        """Procesar un mensaje del usuario"""
        
        if not user_message or not user_message.strip():
            return {'error': 'Mensaje vacío'}
        
        # Obtener o crear conversación
        if session_id not in self.conversation_cache:
            self.start_conversation(session_id)
        
        conversation_data = self.conversation_cache[session_id]
        conversation_id = conversation_data['conversation_id']
        
        # Verificar límite de mensajes
        if len(conversation_data['messages']) >= Config.MAX_CONVERSATION_LENGTH:
            return self._handle_conversation_limit(session_id)
        
        try:
            # 1. Análisis de riesgo del mensaje
            risk_analysis = self.risk_analyzer.analyze_message(user_message)
            
            # 2. Guardar mensaje del usuario en BD (hasheado)
            user_message_hash = hash_message(user_message)
            save_message(
                conversation_id=conversation_id,
                sender='user',
                message_hash=user_message_hash,
                risk_level=risk_analysis['risk_level'],
                violence_type=risk_analysis.get('violence_type'),
                keywords=risk_analysis.get('keywords_found', [])
            )
            
            # 3. Log del análisis
            log_message_processed(
                session_id, 
                user_message_hash, 
                risk_analysis['risk_level'],
                risk_analysis.get('violence_type')
            )
            
            # 4. Actualizar cache de conversación
            conversation_data['messages'].append({
                'role': 'user',
                'content': user_message,
                'timestamp': datetime.utcnow(),
                'risk_analysis': risk_analysis
            })
            conversation_data['risk_history'].append(risk_analysis['risk_level'])
            
            # 5. Generar respuesta según el nivel de riesgo
            if risk_analysis['risk_level'] == 'alto':
                response_data = self._handle_high_risk(session_id, risk_analysis)
            else:
                response_data = self._generate_contextual_response(
                    session_id, user_message, risk_analysis
                )
            
            # 6. Guardar respuesta del bot en BD
            bot_message_hash = hash_message(response_data['message'])
            save_message(
                conversation_id=conversation_id,
                sender='bot',
                message_hash=bot_message_hash,
                response_type=risk_analysis.get('response_type', 'info')
            )
            
            # 7. Actualizar cache con respuesta del bot
            conversation_data['messages'].append({
                'role': 'assistant',
                'content': response_data['message'],
                'timestamp': datetime.utcnow()
            })
            
            return {
                'message': response_data['message'],
                'risk_level': risk_analysis['risk_level'],
                'show_emergency_contacts': response_data.get('show_emergency_contacts', False),
                'emergency_contacts': response_data.get('emergency_contacts'),
                'conversation_id': conversation_id,
                'message_count': len(conversation_data['messages'])
            }
            
        except Exception as e:
            error_message = "Lo siento, he tenido un problema técnico. ¿Podrías repetir tu mensaje?"
            return {
                'message': error_message,
                'error': str(e),
                'conversation_id': conversation_id
            }
    
    def _handle_high_risk(self, session_id, risk_analysis):
        """Manejar situaciones de alto riesgo"""
        log_high_risk_detected(session_id, "HIGH_RISK_MESSAGE")
        log_emergency_contact_provided(session_id, "AUTO_HIGH_RISK")
        
        # Generar respuesta de emergencia
        emergency_response = self.openrouter_service.generate_emergency_response(session_id)
        
        return {
            'message': emergency_response,
            'show_emergency_contacts': True,
            'emergency_contacts': Config.EMERGENCY_CONTACTS
        }
    
    def _generate_contextual_response(self, session_id, user_message, risk_analysis):
        """Generar respuesta contextual usando OpenRouter"""
        
        conversation_data = self.conversation_cache.get(session_id, {})
        conversation_history = conversation_data.get('messages', [])
        
        # Llamar a OpenRouter para generar respuesta
        api_response = self.openrouter_service.generate_response(
            user_message=user_message,
            conversation_history=conversation_history,
            risk_analysis=risk_analysis,
            session_id=session_id
        )
        
        if api_response['success']:
            bot_message = api_response['response']
        else:
            bot_message = api_response['response']  # Respuesta de fallback
        
        # Agregar información de recursos si es riesgo medio
        show_contacts = False
        emergency_contacts = None
        
        if risk_analysis['risk_level'] == 'medio':
            bot_message += "\n\n💜 Si necesitas hablar con alguien más, recuerda que hay líneas de apoyo disponibles."
            show_contacts = True
            emergency_contacts = Config.EMERGENCY_CONTACTS
            log_emergency_contact_provided(session_id, "MEDIUM_RISK")
        
        return {
            'message': bot_message,
            'show_emergency_contacts': show_contacts,
            'emergency_contacts': emergency_contacts
        }
    
    def _handle_conversation_limit(self, session_id):
        """Manejar cuando se alcanza el límite de mensajes"""
        return {
            'message': Config.GOODBYE_MESSAGE,
            'conversation_ended': True,
            'show_emergency_contacts': True,
            'emergency_contacts': Config.EMERGENCY_CONTACTS
        }
    
    def end_conversation(self, session_id):
        """Finalizar una conversación"""
        if session_id in self.conversation_cache:
            conversation_data = self.conversation_cache[session_id]
            
            # Actualizar estado en BD
            from utils.database import Conversation
            conversation = Conversation.query.get(conversation_data['conversation_id'])
            if conversation:
                conversation.status = 'completed'
                db.session.commit()
            
            # Log de finalización
            from utils.logger import log_conversation_end
            log_conversation_end(
                session_id,
                len(conversation_data['messages']),
                max(conversation_data['risk_history']) if conversation_data['risk_history'] else 'bajo'
            )
            
            # Limpiar cache
            del self.conversation_cache[session_id]
        
        return {
            'message': Config.GOODBYE_MESSAGE,
            'show_emergency_contacts': True,
            'emergency_contacts': Config.EMERGENCY_CONTACTS
        }
    
    def get_conversation_summary(self, session_id):
        """Obtener resumen de la conversación actual"""
        if session_id not in self.conversation_cache:
            return None
        
        conversation_data = self.conversation_cache[session_id]
        risk_levels = conversation_data['risk_history']
        
        return {
            'message_count': len(conversation_data['messages']),
            'duration_minutes': (datetime.utcnow() - conversation_data['start_time']).total_seconds() / 60,
            'max_risk_level': max(risk_levels) if risk_levels else 'bajo',
            'risk_distribution': {
                'bajo': risk_levels.count('bajo'),
                'medio': risk_levels.count('medio'),
                'alto': risk_levels.count('alto')
            }
        }
    
    def test_services(self):
        """Probar todos los servicios del chatbot"""
        results = {}
        
        # Probar OpenRouter
        openrouter_ok, openrouter_msg = self.openrouter_service.test_connection()
        results['openrouter'] = {'status': openrouter_ok, 'message': openrouter_msg}
        
        # Probar analizador de riesgo
        try:
            test_analysis = self.risk_analyzer.analyze_message("Hola, ¿cómo estás?")
            results['risk_analyzer'] = {
                'status': True, 
                'message': f"Análisis exitoso: {test_analysis['risk_level']}"
            }
        except Exception as e:
            results['risk_analyzer'] = {'status': False, 'message': str(e)}
        
        return results
