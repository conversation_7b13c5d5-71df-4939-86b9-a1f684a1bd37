from flask import Flask, render_template, request, jsonify, session
import uuid
import os
from config import Config
from utils.database import init_db
from services.chatbot_service import ChatbotService

# Crear aplicación Flask
app = Flask(__name__)
app.config.from_object(Config)

# Inicializar base de datos
init_db(app)

# Inicializar servicio del chatbot
chatbot_service = ChatbotService()

@app.route('/')
def index():
    """Página principal del chatbot"""
    # Generar ID de sesión único si no existe
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())

    return render_template('index.html')

@app.route('/api/start', methods=['POST'])
def start_conversation():
    """Iniciar una nueva conversación"""
    try:
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id

        result = chatbot_service.start_conversation(session_id)

        return jsonify({
            'success': True,
            'message': result['message'],
            'session_id': session_id,
            'conversation_id': result['conversation_id']
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Error al iniciar la conversación. Por favor, intenta de nuevo.'
        }), 500

@app.route('/api/message', methods=['POST'])
def send_message():
    """Enviar un mensaje al chatbot"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()

        if not user_message:
            return jsonify({
                'success': False,
                'error': 'Mensaje vacío'
            }), 400

        session_id = session.get('session_id')
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Sesión no válida'
            }), 400

        # Procesar mensaje
        result = chatbot_service.process_message(session_id, user_message)

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error'],
                'message': result.get('message', 'Error procesando el mensaje')
            }), 500

        response_data = {
            'success': True,
            'message': result['message'],
            'risk_level': result.get('risk_level', 'bajo'),
            'message_count': result.get('message_count', 0),
            'conversation_ended': result.get('conversation_ended', False)
        }

        # Agregar contactos de emergencia si es necesario
        if result.get('show_emergency_contacts'):
            response_data['emergency_contacts'] = result.get('emergency_contacts')

        return jsonify(response_data)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Error procesando tu mensaje. Por favor, intenta de nuevo.'
        }), 500

@app.route('/api/end', methods=['POST'])
def end_conversation():
    """Finalizar la conversación"""
    try:
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Sesión no válida'
            }), 400

        result = chatbot_service.end_conversation(session_id)

        # Limpiar sesión
        session.pop('session_id', None)

        return jsonify({
            'success': True,
            'message': result['message'],
            'emergency_contacts': result.get('emergency_contacts')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Error al finalizar la conversación.'
        }), 500

@app.route('/api/emergency-contacts')
def get_emergency_contacts():
    """Obtener contactos de emergencia"""
    return jsonify({
        'success': True,
        'contacts': Config.EMERGENCY_CONTACTS
    })

@app.route('/api/health')
def health_check():
    """Verificar el estado de la aplicación"""
    try:
        # Probar servicios
        service_status = chatbot_service.test_services()

        all_services_ok = all(
            service['status'] for service in service_status.values()
        )

        return jsonify({
            'success': True,
            'status': 'healthy' if all_services_ok else 'degraded',
            'services': service_status,
            'timestamp': str(datetime.utcnow())
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/api/stats')
def get_stats():
    """Obtener estadísticas básicas (sin datos personales)"""
    try:
        from utils.database import Conversation, Message, RiskAnalysis
        from sqlalchemy import func
        from datetime import date

        # Estadísticas básicas
        total_conversations = Conversation.query.count()
        total_messages = Message.query.count()

        # Distribución de riesgo del día actual
        today = date.today()
        today_conversations = Conversation.query.filter(
            func.date(Conversation.created_at) == today
        ).all()

        risk_distribution = {'bajo': 0, 'medio': 0, 'alto': 0}
        for conv in today_conversations:
            risk_distribution[conv.max_risk_level] += 1

        return jsonify({
            'success': True,
            'stats': {
                'total_conversations': total_conversations,
                'total_messages': total_messages,
                'today_conversations': len(today_conversations),
                'risk_distribution_today': risk_distribution
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Manejar errores 404"""
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Manejar errores 500"""
    return jsonify({
        'success': False,
        'error': 'Error interno del servidor'
    }), 500

if __name__ == '__main__':
    # Crear directorio de logs si no existe
    os.makedirs('logs', exist_ok=True)

    # Importar datetime para health check
    from datetime import datetime

    # Ejecutar aplicación
    app.run(
        debug=Config.DEBUG,
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000))
    )
