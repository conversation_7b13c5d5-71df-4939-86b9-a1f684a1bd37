#!/usr/bin/env python3
"""
Script de ejecución para el Chatbot Preventivo de Violencia Familiar
"""

import os
import sys
from app import app

def main():
    """Función principal para ejecutar la aplicación"""
    
    # Crear directorio de logs si no existe
    os.makedirs('logs', exist_ok=True)
    
    # Verificar que los datasets existen
    required_files = [
        'dataset_chatbot_violencia.csv',
        'dataset_chatbot_violencia_enriquecido.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Error: Faltan archivos requeridos:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPor favor, asegúrate de que los datasets estén en el directorio raíz.")
        sys.exit(1)
    
    # Verificar conexión con OpenRouter
    print("🔍 Verificando servicios...")
    
    try:
        from services.chatbot_service import ChatbotService
        chatbot = ChatbotService()
        service_status = chatbot.test_services()
        
        print("📊 Estado de servicios:")
        for service, status in service_status.items():
            icon = "✅" if status['status'] else "❌"
            print(f"   {icon} {service}: {status['message']}")
        
        # Verificar si todos los servicios están funcionando
        all_ok = all(status['status'] for status in service_status.values())
        
        if not all_ok:
            print("\n⚠️  Advertencia: Algunos servicios no están funcionando correctamente.")
            print("La aplicación puede funcionar con funcionalidad limitada.")
            
            response = input("¿Deseas continuar? (s/n): ")
            if response.lower() not in ['s', 'si', 'sí', 'y', 'yes']:
                print("Cancelando ejecución.")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error al verificar servicios: {e}")
        print("La aplicación puede no funcionar correctamente.")
        
        response = input("¿Deseas continuar de todos modos? (s/n): ")
        if response.lower() not in ['s', 'si', 'sí', 'y', 'yes']:
            print("Cancelando ejecución.")
            sys.exit(1)
    
    # Mostrar información de inicio
    print("\n" + "="*50)
    print("🤖 CHATBOT PREVENTIVO DE VIOLENCIA FAMILIAR")
    print("="*50)
    print(f"🌐 URL: http://localhost:{os.environ.get('PORT', 5000)}")
    print(f"🔧 Modo debug: {'Activado' if app.config['DEBUG'] else 'Desactivado'}")
    print(f"📁 Base de datos: {app.config['SQLALCHEMY_DATABASE_URI']}")
    print("="*50)
    print("📝 Logs se guardarán en: logs/chatbot.log")
    print("🔒 Privacidad: Sin almacenamiento de datos personales")
    print("="*50)
    print("\n✨ Iniciando aplicación...")
    print("   Presiona Ctrl+C para detener\n")
    
    # Ejecutar aplicación
    try:
        app.run(
            debug=app.config['DEBUG'],
            host='0.0.0.0',
            port=int(os.environ.get('PORT', 5000))
        )
    except KeyboardInterrupt:
        print("\n\n👋 Aplicación detenida por el usuario.")
        print("¡Gracias por usar el Chatbot Preventivo!")
    except Exception as e:
        print(f"\n❌ Error al ejecutar la aplicación: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
