/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8B5A96;
    --secondary-color: #B19CD9;
    --accent-color: #E8D5E8;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --text-primary: #2C2C2C;
    --text-secondary: #666666;
    --background: #F5F5F5;
    --white: #FFFFFF;
    --border-color: #E0E0E0;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--background) 100%);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
}

/* Contenedor principal */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: var(--white);
    box-shadow: var(--shadow);
}

/* Header del chat */
.chat-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bot-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bot-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.bot-details h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.status {
    font-size: 0.9rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.emergency-btn, .end-chat-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.emergency-btn:hover {
    background: var(--danger-color);
}

.end-chat-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Área de mensajes */
.chat-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Estilos de mensajes */
.message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease;
}

.message.user {
    justify-content: flex-end;
}

.message.bot {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    position: relative;
    word-wrap: break-word;
}

.message.user .message-content {
    background: var(--primary-color);
    color: var(--white);
    border-bottom-right-radius: 4px;
}

.message.bot .message-content {
    background: var(--white);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* Indicador de escritura */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.typing-dots {
    display: flex;
    gap: 0.2rem;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--secondary-color);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Área de entrada */
.chat-input-area {
    background: var(--white);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

#message-input {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    max-height: 120px;
    transition: var(--transition);
}

#message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 90, 150, 0.2);
}

.send-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: var(--secondary-color);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
}

.input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.privacy-note {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Modal de emergencia */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: var(--danger-color);
    color: var(--white);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
}

.close-modal {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close-modal:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 1rem;
}

.emergency-contact {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: var(--background);
    transition: var(--transition);
}

.emergency-contact:hover {
    background: var(--accent-color);
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--white);
    flex-shrink: 0;
}

.contact-icon.emergency {
    background: var(--danger-color);
}

.contact-icon.support {
    background: var(--primary-color);
}

.contact-icon.report {
    background: var(--warning-color);
}

.contact-icon.location {
    background: var(--success-color);
}

.contact-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.phone {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.address {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.description {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.modal-footer {
    background: var(--background);
    padding: 1rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.important-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--danger-color);
    font-weight: 600;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .app-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .bot-details h1 {
        font-size: 1rem;
    }
    
    .status {
        font-size: 0.8rem;
    }
}

/* Estados de riesgo */
.message.bot.risk-high .message-content {
    border-left: 4px solid var(--danger-color);
    background: #ffebee;
}

.message.bot.risk-medium .message-content {
    border-left: 4px solid var(--warning-color);
    background: #fff8e1;
}

.message.bot.risk-low .message-content {
    border-left: 4px solid var(--success-color);
}

/* Scrollbar personalizado */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--background);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
