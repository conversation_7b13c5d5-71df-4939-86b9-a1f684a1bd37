// Variables globales
let isConnected = false;
let conversationStarted = false;
let messageCount = 0;

// Elementos del DOM
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendBtn = document.getElementById('send-btn');
const typingIndicator = document.getElementById('typing-indicator');
const emergencyBtn = document.getElementById('emergency-btn');
const endChatBtn = document.getElementById('end-chat-btn');
const emergencyModal = document.getElementById('emergency-modal');
const loadingOverlay = document.getElementById('loading-overlay');
const charCount = document.querySelector('.char-count');

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    setupEventListeners();
});

function initializeChat() {
    showLoading();
    
    // Iniciar conversación
    fetch('/api/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            isConnected = true;
            conversationStarted = true;
            addMessage(data.message, 'bot');
            messageInput.focus();
        } else {
            showError('Error al conectar con el asistente. Por favor, recarga la página.');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showError('Error de conexión. Por favor, verifica tu internet y recarga la página.');
    });
}

function setupEventListeners() {
    // Envío de mensajes
    sendBtn.addEventListener('click', sendMessage);
    
    // Enter para enviar (Shift+Enter para nueva línea)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // Auto-resize del textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        
        // Actualizar contador de caracteres
        const length = this.value.length;
        charCount.textContent = `${length}/500`;
        
        // Habilitar/deshabilitar botón de envío
        sendBtn.disabled = length === 0 || length > 500;
    });
    
    // Botón de emergencia
    emergencyBtn.addEventListener('click', showEmergencyContacts);
    
    // Botón de finalizar chat
    endChatBtn.addEventListener('click', endConversation);
    
    // Modal de emergencia
    const closeModal = document.querySelector('.close-modal');
    closeModal.addEventListener('click', hideEmergencyContacts);
    
    emergencyModal.addEventListener('click', function(e) {
        if (e.target === emergencyModal) {
            hideEmergencyContacts();
        }
    });
    
    // Escape para cerrar modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && emergencyModal.style.display === 'block') {
            hideEmergencyContacts();
        }
    });
}

function sendMessage() {
    const message = messageInput.value.trim();
    
    if (!message || !isConnected) return;
    
    // Agregar mensaje del usuario
    addMessage(message, 'user');
    
    // Limpiar input
    messageInput.value = '';
    messageInput.style.height = 'auto';
    charCount.textContent = '0/500';
    sendBtn.disabled = true;
    
    // Mostrar indicador de escritura
    showTyping();
    
    // Enviar mensaje al servidor
    fetch('/api/message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        hideTyping();
        
        if (data.success) {
            // Agregar respuesta del bot
            addMessage(data.message, 'bot', data.risk_level);
            
            // Mostrar contactos de emergencia si es necesario
            if (data.emergency_contacts) {
                setTimeout(() => {
                    showEmergencyContacts(data.emergency_contacts);
                }, 1000);
            }
            
            // Verificar si la conversación terminó
            if (data.conversation_ended) {
                endConversation();
            }
            
            messageCount = data.message_count || messageCount + 1;
        } else {
            addMessage(data.message || 'Lo siento, hubo un error. ¿Puedes intentar de nuevo?', 'bot', 'error');
        }
        
        messageInput.focus();
    })
    .catch(error => {
        hideTyping();
        console.error('Error:', error);
        addMessage('Error de conexión. Por favor, verifica tu internet e intenta de nuevo.', 'bot', 'error');
        messageInput.focus();
    });
}

function addMessage(content, sender, riskLevel = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    if (riskLevel) {
        messageDiv.classList.add(`risk-${riskLevel}`);
    }
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    
    // Formatear contenido (convertir saltos de línea y enlaces)
    const formattedContent = formatMessageContent(content);
    messageContent.innerHTML = formattedContent;
    
    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    messageContent.appendChild(messageTime);
    messageDiv.appendChild(messageContent);
    chatMessages.appendChild(messageDiv);
    
    // Scroll al final
    scrollToBottom();
}

function formatMessageContent(content) {
    // Convertir saltos de línea a <br>
    let formatted = content.replace(/\n/g, '<br>');
    
    // Convertir números de teléfono en enlaces
    formatted = formatted.replace(/(\d{3}-\d{3}-\d{2}-\d{2})/g, '<a href="tel:$1">$1</a>');
    formatted = formatted.replace(/(\d{3})/g, '<a href="tel:$1">$1</a>');
    
    // Resaltar texto importante
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/🚨/g, '<span style="color: #F44336;">🚨</span>');
    formatted = formatted.replace(/💜/g, '<span style="color: #8B5A96;">💜</span>');
    
    return formatted;
}

function showTyping() {
    typingIndicator.style.display = 'flex';
    scrollToBottom();
}

function hideTyping() {
    typingIndicator.style.display = 'none';
}

function scrollToBottom() {
    setTimeout(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }, 100);
}

function showEmergencyContacts(contacts = null) {
    emergencyModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // Si se proporcionan contactos específicos, actualizarlos
    if (contacts) {
        updateEmergencyContactsModal(contacts);
    }
}

function hideEmergencyContacts() {
    emergencyModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function updateEmergencyContactsModal(contacts) {
    // Esta función podría actualizar dinámicamente los contactos
    // Por ahora, los contactos están hardcodeados en el HTML
    console.log('Contactos de emergencia:', contacts);
}

function endConversation() {
    if (!conversationStarted) return;
    
    const confirmEnd = confirm('¿Estás segura de que quieres finalizar la conversación?');
    if (!confirmEnd) return;
    
    showLoading();
    
    fetch('/api/end', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            addMessage(data.message, 'bot');
            
            // Mostrar contactos de emergencia
            setTimeout(() => {
                showEmergencyContacts(data.emergency_contacts);
            }, 1000);
            
            // Deshabilitar input
            messageInput.disabled = true;
            sendBtn.disabled = true;
            endChatBtn.disabled = true;
            
            conversationStarted = false;
            isConnected = false;
            
            // Opción de reiniciar después de un tiempo
            setTimeout(() => {
                const restart = confirm('¿Te gustaría iniciar una nueva conversación?');
                if (restart) {
                    location.reload();
                }
            }, 5000);
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showError('Error al finalizar la conversación.');
    });
}

function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

function showError(message) {
    addMessage(message, 'bot', 'error');
}

// Funciones de utilidad
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Mostrar feedback visual
        const feedback = document.createElement('div');
        feedback.textContent = 'Copiado al portapapeles';
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #4CAF50;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            z-index: 3000;
        `;
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            document.body.removeChild(feedback);
        }, 2000);
    });
}

// Agregar funcionalidad de copia a números de teléfono
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.href.startsWith('tel:')) {
        e.preventDefault();
        const phoneNumber = e.target.textContent;
        copyToClipboard(phoneNumber);
    }
});

// Manejo de errores de red
window.addEventListener('online', function() {
    if (!isConnected && conversationStarted) {
        addMessage('Conexión restaurada. Puedes continuar la conversación.', 'bot');
        isConnected = true;
    }
});

window.addEventListener('offline', function() {
    if (isConnected) {
        addMessage('Se perdió la conexión a internet. Verifica tu conexión.', 'bot', 'error');
        isConnected = false;
    }
});

// Prevenir cierre accidental de la página
window.addEventListener('beforeunload', function(e) {
    if (conversationStarted && messageCount > 1) {
        e.preventDefault();
        e.returnValue = '¿Estás segura de que quieres salir? Se perderá la conversación actual.';
    }
});
