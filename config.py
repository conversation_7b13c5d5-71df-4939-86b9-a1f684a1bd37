import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Configuración de la aplicación"""
    
    # Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # OpenRouter API
    OPENROUTER_API_KEY = "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"
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    OPENROUTER_MODEL = "anthropic/claude-3-haiku"  # Modelo económico y eficiente
    
    # Base de datos
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///chatbot_violencia.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Datasets
    DATASET_BASIC = 'dataset_chatbot_violencia.csv'
    DATASET_ENRICHED = 'dataset_chatbot_violencia_enriquecido.csv'
    
    # Configuración del chatbot
    MAX_CONVERSATION_LENGTH = 50  # Máximo de mensajes por conversación
    RISK_THRESHOLD_HIGH = 0.8
    RISK_THRESHOLD_MEDIUM = 0.5
    
    # Recursos de ayuda locales - Ensenada, BC
    EMERGENCY_CONTACTS = {
        'linea_violeta': {
            'nombre': 'Línea Violeta',
            'telefono': '646-182-30-00 ext. 2743',
            'descripcion': 'Línea especializada en violencia de género'
        },
        'emergencia': {
            'nombre': 'Emergencias',
            'telefono': '911',
            'descripcion': 'Número de emergencia nacional'
        },
        'denuncia_anonima': {
            'nombre': 'Denuncia Anónima',
            'telefono': '089',
            'descripcion': 'Línea de denuncia anónima'
        },
        'subcentro_seguridad': {
            'nombre': 'Subcentro de Seguridad Ensenada',
            'telefono': '646-176-03-77',
            'direccion': 'Prolongación Blvd. Zertuche 6474, Edificio H, Col. Praderas de Ciprés',
            'descripcion': 'Centro de atención presencial'
        }
    }
    
    # Configuración de logs
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/chatbot.log'
    
    # Mensajes del sistema
    WELCOME_MESSAGE = """¡Hola! Soy un asistente virtual especializado en prevención de violencia familiar. 

Estoy aquí para escucharte y brindarte apoyo. Puedes contarme lo que está pasando en tu vida de manera confidencial y segura.

¿En qué puedo ayudarte hoy?"""

    GOODBYE_MESSAGE = """Gracias por confiar en mí. Recuerda que:

• No estás sola/o
• La violencia no es tu culpa
• Hay ayuda disponible
• Tu seguridad es lo más importante

Si necesitas ayuda inmediata, no dudes en contactar:
📞 Emergencias: 911
📞 Línea Violeta: 646-182-30-00 ext. 2743

¡Cuídate mucho! 💜"""
