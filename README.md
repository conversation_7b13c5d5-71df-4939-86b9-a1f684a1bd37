# Chatbot Preventivo de Violencia Familiar

Una aplicación web con chatbot especializado en prevención de violencia familiar, potenciado por inteligencia artificial a través de OpenRouter API.

## Características

- 🤖 **Chatbot inteligente** con modelo de lenguaje avanzado
- 🔍 **Análisis de riesgo** automático en tiempo real
- 💜 **Interfaz empática** tipo WhatsApp/Messenger
- 🚨 **Contactos de emergencia** locales de Ensenada, BC
- 🔒 **Privacidad y seguridad** - sin almacenamiento de datos personales
- 📊 **Sistema de logs** controlados para análisis interno

## Tecnologías

- **Backend**: Python + Flask
- **Frontend**: HTML5, CSS3, JavaScript vanilla
- **IA**: OpenRouter API (Claude 3 Haiku)
- **Base de datos**: SQLite
- **Análisis**: scikit-learn, pandas

## Instalación

### Prerrequisitos

- Python 3.8 o superior
- pip (gestor de paquetes de Python)

### Pasos de instalación

1. **Clonar el repositorio**
   ```bash
   git clone <url-del-repositorio>
   cd PythonChatbotPreventivovf
   ```

2. **Crear entorno virtual**
   ```bash
   python -m venv venv
   
   # En Windows
   venv\Scripts\activate
   
   # En Linux/Mac
   source venv/bin/activate
   ```

3. **Instalar dependencias**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configurar variables de entorno**
   ```bash
   cp .env.example .env
   # Editar .env con tus configuraciones
   ```

5. **Ejecutar la aplicación**
   ```bash
   python app.py
   ```

6. **Abrir en el navegador**
   ```
   http://localhost:5000
   ```

## Estructura del Proyecto

```
PythonChatbotPreventivovf/
├── app.py                              # Aplicación Flask principal
├── config.py                           # Configuración
├── requirements.txt                    # Dependencias
├── README.md                          # Este archivo
├── .env.example                       # Ejemplo de variables de entorno
├── dataset_chatbot_violencia.csv      # Dataset básico
├── dataset_chatbot_violencia_enriquecido.csv  # Dataset enriquecido
├── models/
│   └── risk_analyzer.py               # Motor de análisis de riesgo
├── services/
│   ├── openrouter_service.py          # Integración con OpenRouter
│   └── chatbot_service.py             # Lógica principal del chatbot
├── utils/
│   ├── database.py                    # Configuración de base de datos
│   └── logger.py                      # Sistema de logging
├── templates/
│   └── index.html                     # Interfaz principal
├── static/
│   ├── css/
│   │   └── style.css                  # Estilos de la aplicación
│   └── js/
│       └── chat.js                    # Funcionalidad del chat
└── logs/                              # Directorio de logs (se crea automáticamente)
```

## Funcionalidades

### Motor de Análisis de Riesgo

- **Análisis semántico** usando TF-IDF y similitud coseno
- **Detección de patrones** específicos de violencia
- **Clasificación por palabras clave** según tipo de violencia
- **Niveles de riesgo**: bajo, medio, alto

### Tipos de Violencia Detectados

- **Física**: Golpes, empujones, agresiones corporales
- **Psicológica**: Insultos, amenazas, control emocional
- **Sexual**: Violencia sexual, acoso
- **Económica**: Control financiero, restricciones laborales
- **Digital**: Acoso en redes, control de dispositivos

### Contactos de Emergencia (Ensenada, BC)

- **Emergencias**: 911
- **Línea Violeta**: 646-182-30-00 ext. 2743
- **Denuncia Anónima**: 089
- **Subcentro de Seguridad**: 646-176-03-77

## API Endpoints

- `GET /` - Página principal
- `POST /api/start` - Iniciar conversación
- `POST /api/message` - Enviar mensaje
- `POST /api/end` - Finalizar conversación
- `GET /api/emergency-contacts` - Obtener contactos de emergencia
- `GET /api/health` - Estado de la aplicación
- `GET /api/stats` - Estadísticas básicas

## Configuración

### Variables de Entorno

```env
SECRET_KEY=tu-clave-secreta
FLASK_DEBUG=False
DATABASE_URL=sqlite:///chatbot_violencia.db
PORT=5000
```

### OpenRouter API

La API key de OpenRouter está configurada en `config.py`. Para usar tu propia key:

1. Registrarse en [OpenRouter](https://openrouter.ai/)
2. Obtener API key
3. Modificar `OPENROUTER_API_KEY` en `config.py`

## Seguridad y Privacidad

- ✅ **Sin almacenamiento de mensajes**: Solo se guardan hashes
- ✅ **Logs anonimizados**: Sin datos personales identificables
- ✅ **Sesiones temporales**: Se limpian automáticamente
- ✅ **HTTPS recomendado**: Para producción
- ✅ **Análisis agregado**: Solo estadísticas generales

## Desarrollo

### Ejecutar en modo desarrollo

```bash
export FLASK_DEBUG=True  # Linux/Mac
set FLASK_DEBUG=True     # Windows
python app.py
```

### Ejecutar tests

```bash
python -m pytest tests/  # Cuando se implementen
```

## Despliegue

### Usando Gunicorn (Producción)

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Variables de entorno para producción

```env
SECRET_KEY=clave-secreta-fuerte
FLASK_DEBUG=False
DATABASE_URL=postgresql://usuario:password@host:puerto/db  # Para PostgreSQL
```

## Contribuir

1. Fork del proyecto
2. Crear rama para feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit de cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## Contacto y Soporte

Para soporte técnico o consultas sobre el proyecto, contactar al equipo de desarrollo.

## Recursos Adicionales

- [Documentación de Flask](https://flask.palletsprojects.com/)
- [OpenRouter API Docs](https://openrouter.ai/docs)
- [Línea Nacional contra la Violencia](https://www.gob.mx/inmujeres)

---

**Nota importante**: Este chatbot es una herramienta de apoyo y orientación. En situaciones de emergencia, siempre contactar directamente a los servicios de emergencia (911) o a las autoridades locales.
