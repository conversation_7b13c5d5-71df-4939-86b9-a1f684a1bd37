from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

db = SQLAlchemy()

class Conversation(db.Model):
    """Modelo para almacenar conversaciones (sin datos personales)"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = db.Column(db.String(36), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    message_count = db.Column(db.Integer, default=0)
    max_risk_level = db.Column(db.String(10), default='bajo')  # bajo, medio, alto
    status = db.Column(db.String(20), default='active')  # active, completed, emergency
    
    # Relación con mensajes
    messages = db.relationship('Message', backref='conversation', lazy=True, cascade='all, delete-orphan')

class Message(db.Model):
    """Modelo para almacenar mensajes (anonimizados)"""
    __tablename__ = 'messages'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = db.Column(db.String(36), db.ForeignKey('conversations.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    sender = db.Column(db.String(10), nullable=False)  # 'user' o 'bot'
    message_hash = db.Column(db.String(64))  # Hash del mensaje para análisis sin exponer contenido
    risk_level = db.Column(db.String(10))  # bajo, medio, alto
    violence_type = db.Column(db.String(20))  # física, psicológica, sexual, económica, digital
    keywords_detected = db.Column(db.Text)  # Palabras clave detectadas (separadas por comas)
    response_type = db.Column(db.String(20))  # info, support, emergency, resources

class RiskAnalysis(db.Model):
    """Modelo para almacenar análisis de riesgo agregados"""
    __tablename__ = 'risk_analysis'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    date = db.Column(db.Date, default=datetime.utcnow().date())
    total_conversations = db.Column(db.Integer, default=0)
    low_risk_count = db.Column(db.Integer, default=0)
    medium_risk_count = db.Column(db.Integer, default=0)
    high_risk_count = db.Column(db.Integer, default=0)
    emergency_contacts_provided = db.Column(db.Integer, default=0)
    
    # Tipos de violencia detectados
    physical_violence = db.Column(db.Integer, default=0)
    psychological_violence = db.Column(db.Integer, default=0)
    sexual_violence = db.Column(db.Integer, default=0)
    economic_violence = db.Column(db.Integer, default=0)
    digital_violence = db.Column(db.Integer, default=0)

def init_db(app):
    """Inicializar la base de datos"""
    db.init_app(app)
    
    with app.app_context():
        db.create_all()
        print("Base de datos inicializada correctamente")

def get_or_create_conversation(session_id):
    """Obtener o crear una conversación"""
    conversation = Conversation.query.filter_by(
        session_id=session_id, 
        status='active'
    ).first()
    
    if not conversation:
        conversation = Conversation(session_id=session_id)
        db.session.add(conversation)
        db.session.commit()
    
    return conversation

def save_message(conversation_id, sender, message_hash, risk_level=None, 
                violence_type=None, keywords=None, response_type=None):
    """Guardar un mensaje en la base de datos"""
    message = Message(
        conversation_id=conversation_id,
        sender=sender,
        message_hash=message_hash,
        risk_level=risk_level,
        violence_type=violence_type,
        keywords_detected=','.join(keywords) if keywords else None,
        response_type=response_type
    )
    
    db.session.add(message)
    
    # Actualizar contador de mensajes en la conversación
    conversation = Conversation.query.get(conversation_id)
    if conversation:
        conversation.message_count += 1
        conversation.updated_at = datetime.utcnow()
        
        # Actualizar el nivel máximo de riesgo
        if risk_level:
            risk_levels = {'bajo': 1, 'medio': 2, 'alto': 3}
            current_max = risk_levels.get(conversation.max_risk_level, 1)
            new_level = risk_levels.get(risk_level, 1)
            
            if new_level > current_max:
                conversation.max_risk_level = risk_level
    
    db.session.commit()
    return message
