import logging
import os
import hashlib
from datetime import datetime
from config import Config

def setup_logger():
    """Configurar el sistema de logging sin datos personales"""
    
    # Crear directorio de logs si no existe
    log_dir = os.path.dirname(Config.LOG_FILE)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configurar el logger
    logger = logging.getLogger('chatbot_violencia')
    logger.setLevel(getattr(logging, Config.LOG_LEVEL))
    
    # Evitar duplicar handlers
    if logger.handlers:
        return logger
    
    # Handler para archivo
    file_handler = logging.FileHandler(Config.LOG_FILE, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # Handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formato de logs
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def hash_message(message):
    """Crear hash de un mensaje para logging sin exponer contenido"""
    return hashlib.sha256(message.encode('utf-8')).hexdigest()

def log_conversation_start(session_id):
    """Log del inicio de conversación"""
    logger = logging.getLogger('chatbot_violencia')
    logger.info(f"Nueva conversación iniciada - Session: {session_id}")

def log_message_processed(session_id, message_hash, risk_level, violence_type=None):
    """Log de mensaje procesado"""
    logger = logging.getLogger('chatbot_violencia')
    logger.info(
        f"Mensaje procesado - Session: {session_id}, "
        f"Hash: {message_hash[:16]}..., "
        f"Riesgo: {risk_level}, "
        f"Tipo: {violence_type or 'N/A'}"
    )

def log_emergency_contact_provided(session_id, contact_type):
    """Log cuando se proporcionan contactos de emergencia"""
    logger = logging.getLogger('chatbot_violencia')
    logger.warning(
        f"Contacto de emergencia proporcionado - Session: {session_id}, "
        f"Tipo: {contact_type}"
    )

def log_high_risk_detected(session_id, message_hash):
    """Log cuando se detecta alto riesgo"""
    logger = logging.getLogger('chatbot_violencia')
    logger.critical(
        f"ALTO RIESGO DETECTADO - Session: {session_id}, "
        f"Hash: {message_hash[:16]}..."
    )

def log_error(session_id, error_message, error_type="GENERAL"):
    """Log de errores"""
    logger = logging.getLogger('chatbot_violencia')
    logger.error(
        f"Error {error_type} - Session: {session_id}, "
        f"Error: {error_message}"
    )

def log_api_call(session_id, api_name, status, response_time=None):
    """Log de llamadas a APIs externas"""
    logger = logging.getLogger('chatbot_violencia')
    log_message = f"API Call - Session: {session_id}, API: {api_name}, Status: {status}"
    if response_time:
        log_message += f", Time: {response_time:.2f}s"
    logger.info(log_message)

def log_conversation_end(session_id, message_count, max_risk_level):
    """Log del fin de conversación"""
    logger = logging.getLogger('chatbot_violencia')
    logger.info(
        f"Conversación finalizada - Session: {session_id}, "
        f"Mensajes: {message_count}, "
        f"Riesgo máximo: {max_risk_level}"
    )

# Inicializar logger al importar el módulo
setup_logger()
